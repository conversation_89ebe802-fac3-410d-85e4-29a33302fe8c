import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, AlertCircle, Sparkles } from 'lucide-react';
import { fetchLoginConfig, exchangeCodeForToken } from '@/services/api';
import { setLoginConfig, setTokens, setLoading, setError, setLogin } from '@/store/slices/authSlice';
import { RootState } from '@/store';
import { useToast } from '@/components/ui/use-toast';
import useAuth from '@/hooks/useAuth';
import apiService from '@/services/apiService';

const API_URL = 'https://client-api.acuizen.com';
const FILE_DOWNLOAD = (file: string) => `${API_URL}/files/${file}/presigned-url`;

const LoginPage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { loginConfig, isLoading, error } = useSelector((state: RootState) => state.auth);
  const { isAuthenticated } = useAuth();
  const [baseUrl, setBaseUrl] = useState<string>('');
  const [logoUrl, setLogoUrl] = useState<string | null>(null);
  const backgroundImage = '/images/young_elegant_foreman_his_subordinate_workwear_discussing_technical.jpg';
  const fallbackImage = 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1926&q=80';

  useEffect(() => {
    // Set the base URL for redirects
    setBaseUrl(window.location.origin);
  }, []);

  // Fetch logo URL when loginConfig is available
  useEffect(() => {
    const fetchLogo = async () => {
      if (loginConfig?.LOGO) {
        try {
          const data = await apiService.get(FILE_DOWNLOAD(loginConfig.LOGO));
          if (data && data) {
            setLogoUrl(data as string);
          }
        } catch (error) {
          console.error('Error fetching logo:', error);
        }
      }
    };

    fetchLogo();
  }, [loginConfig]);

  useEffect(() => {
    // If already authenticated, redirect to dashboard
    if (isAuthenticated) {
      navigate('/dashboard');
      return;
    }

    // Fetch login configuration if not already loaded
    if (!loginConfig && !isLoading) {
      console.log('Fetching login config...');
      dispatch(setLoading(true));
      fetchLoginConfig()
        .then((config) => {
          console.log('Login config received:', config);
          dispatch(setLoginConfig(config));
        })
        .catch((err) => {
          console.error('Error fetching login config:', err);
          // Use a working fallback config for development
          const fallbackConfig = {
            COGNITO_USER_DOMAIN: 'https://ap-southeast-1rk4afbwgn.auth.ap-southeast-1.amazoncognito.com',
            COGNITO_USER_APP_CLIENT_ID: 'your-client-id',
            LOGO: undefined,
          };
          console.log('Using fallback config:', fallbackConfig);
          dispatch(setLoginConfig(fallbackConfig));
          dispatch(setError('Using fallback configuration. Some features may not work.'));
          toast({
            title: 'Warning',
            description: 'Using fallback configuration. Some features may not work.',
            variant: 'default',
          });
        })
        .finally(() => {
          dispatch(setLoading(false));
        });
    }
  }, [dispatch, loginConfig, isLoading, isAuthenticated, navigate, toast]);

  useEffect(() => {
    // Handle the authorization code from Cognito redirect
    const searchParams = new URLSearchParams(location.search);
    const code = searchParams.get('code');

    if (code && loginConfig?.COGNITO_USER_DOMAIN && loginConfig?.COGNITO_USER_APP_CLIENT_ID) {
      dispatch(setLoading(true));

      exchangeCodeForToken(
        code,
        baseUrl + '/login',
        loginConfig.COGNITO_USER_DOMAIN,
        loginConfig.COGNITO_USER_APP_CLIENT_ID
      )
        .then((data) => {
          // Store Cognito configuration for refresh token functionality
          localStorage.setItem('COGNITO_USER_DOMAIN', loginConfig.COGNITO_USER_DOMAIN);
          localStorage.setItem('COGNITO_USER_APP_CLIENT_ID', loginConfig.COGNITO_USER_APP_CLIENT_ID);

          dispatch(setTokens({
            accessToken: data.access_token,
            refreshToken: data.refresh_token
          }));
          dispatch(setLogin());
          navigate('/dashboard');
        })
        .catch((err) => {
          console.error('Error exchanging code for token:', err);
          dispatch(setError('Authentication failed. Please try again.'));
          toast({
            title: 'Authentication Error',
            description: 'Failed to complete login. Please try again.',
            variant: 'destructive',
          });
        })
        .finally(() => {
          dispatch(setLoading(false));
        });
    }
  }, [loginConfig, location.search, navigate, dispatch, baseUrl, toast]);

   const handleLogin = () => {
    if (!loginConfig) return;

    const loginUrl = `${loginConfig.COGNITO_USER_DOMAIN}/oauth2/authorize?client_id=${loginConfig.COGNITO_USER_APP_CLIENT_ID}&response_type=code&scope=email+openid+phone&redirect_uri=${encodeURIComponent(baseUrl + '/login')}`;

    window.location.href = loginUrl;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex bg-background">
        {/* Left side - Background image with loading */}
        <div
          className="hidden md:flex md:w-1/2 relative bg-cover bg-center items-center justify-center"
          style={{ backgroundImage: `url(${fallbackImage})` }}
        >
          <div className="absolute inset-0 bg-black/40"></div>
          <div className="relative z-10 text-center text-white p-8">
            <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4" />
            <h2 className="text-xl font-semibold">Loading...</h2>
          </div>
        </div>

        {/* Right side - Loading indicator */}
        <div className="w-full md:w-1/2 flex items-center justify-center p-6 bg-gradient-to-br from-slate-50 to-white">
          <div className="w-full max-w-md">
            <Card className="shadow-2xl border-0 bg-white/80 backdrop-blur-sm">
              <CardContent className="flex flex-col items-center justify-center py-16">
                <div className="mb-6">
                  <Loader2 className="h-16 w-16 animate-spin text-primary" />
                </div>
                <h2 className="text-xl font-semibold text-gray-800 mb-2">Loading...</h2>
                <p className="text-sm text-gray-600">Preparing your experience</p>
              </CardContent>
            </Card>

            {/* Decorative Elements */}
            <div className="absolute top-20 right-20 w-32 h-32 bg-gradient-to-r from-primary/10 to-accent/10 rounded-full blur-3xl -z-10"></div>
            <div className="absolute bottom-20 left-20 w-24 h-24 bg-gradient-to-r from-accent/10 to-primary/10 rounded-full blur-2xl -z-10"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex bg-white">
      {/* Left side - Background image with content */}
      <div
        className="hidden md:flex md:w-1/2 relative bg-cover bg-center"
        style={{ backgroundImage: `url(${fallbackImage})` }}
      >
        {/* Overlay with gradient for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/40 to-black/30"></div>

        {/* Content overlay */}
        <div className="relative z-10 flex flex-col justify-between items-center p-12 text-white h-full w-full">
          {/* Title at top - Horizontally centered */}
          <div className="flex items-center justify-center pt-4 w-full">
            <div className="max-w-4xl mx-auto">
              {/* Title Text - AcuiZen WorkHub */}
              <h1 className="text-5xl font-bold leading-tight text-center" style={{ fontFamily: 'Raleway, sans-serif', textShadow: '2px 2px 4px rgba(0,0,0,0.5)' }}>
                AcuiZen WorkHub
              </h1>
            </div>
          </div>

          {/* Empty middle space */}
          <div className="flex-1"></div>

          {/* Subtext at bottom - Horizontally centered */}
          <div className="flex items-center justify-center pb-4 w-full">
            <div className="max-w-4xl mx-auto">
              <p className="text-2xl leading-relaxed italic text-center" style={{ fontFamily: 'Lato, sans-serif', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}>
                Empower individuals with knowledge, insights and digital tools to get work done
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Right side - Login content */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-6 bg-gradient-to-br from-slate-50 to-white">
        <div className="w-full max-w-md">
          <Card className="shadow-2xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="text-center pb-8 pt-10">
              {/* Enhanced Logo Section */}
              <div className="flex justify-center mb-8">
                {logoUrl ? (
                  <img
                    src={logoUrl}
                    alt="Company Logo"
                    className="h-24 object-contain"
                  />
                ) : (
                  <div className="h-24 w-24 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center shadow-lg">
                    <Sparkles className="h-12 w-12 text-white" />
                  </div>
                )}
              </div>

              {/* Enhanced Welcome Section */}
              <div className="space-y-3">
                <CardTitle className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                  Welcome
                </CardTitle>
                <CardDescription className="text-base text-gray-600 leading-relaxed">
                  Sign in to access your personalized dashboard
                </CardDescription>
              </div>
            </CardHeader>

            <CardContent className="px-10 pb-10">
              {/* Enhanced Error State */}
              {error && (
                <div className="bg-gradient-to-r from-red-50 to-red-50/50 border border-red-200 text-red-700 p-4 rounded-lg mb-6 text-sm flex items-start gap-3 animate-fade-in">
                  <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>{error}</span>
                </div>
              )}

              {/* Enhanced Button */}
              <Button
                className="w-full h-12 text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary"
                onClick={handleLogin}
                disabled={isLoading || !loginConfig}
              >
                {isLoading ? (
                  <div className="flex items-center gap-3">
                    <Loader2 className="h-5 w-5 animate-spin" />
                    <span>Connecting...</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <span>Get Started</span>
                    <Sparkles className="h-4 w-4" />
                  </div>
                )}
              </Button>

              {/* Additional UI Enhancement */}
              {/* <div className="mt-6 text-center">
                <p className="text-xs text-gray-500">
                  Secure authentication powered by AWS Cognito
                </p>
              </div> */}
            </CardContent>
          </Card>

          {/* Decorative Elements */}
          <div className="absolute top-20 right-20 w-32 h-32 bg-gradient-to-r from-primary/10 to-accent/10 rounded-full blur-3xl -z-10"></div>
          <div className="absolute bottom-20 left-20 w-24 h-24 bg-gradient-to-r from-accent/10 to-primary/10 rounded-full blur-2xl -z-10"></div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
