import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { store } from '@/store';
import { setLogout } from '@/store/slices/authSlice';
import { LoginConfig } from '@/store/slices/authSlice';

const API_BASE_URL = 'https://client-api.acuizen.com';

class ApiService {
  private axiosInstance: AxiosInstance;

  constructor(baseURL: string = API_BASE_URL) {
    this.axiosInstance = axios.create({
      baseURL,
      timeout: 30000, // 30 seconds timeout
      headers: {
        'Content-Type': 'application/json',
        'x-enterprise-id': 'koach',
      },
    });

    // Request interceptor to add auth token
    this.axiosInstance.interceptors.request.use(
      (config) => {
        const state = store.getState();
        const token = state.auth.tokens?.accessToken;

        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Log headers for debugging in development only
        if (process.env.NODE_ENV === 'development') {
          console.log('API Request Headers:', {
            'x-enterprise-id': config.headers['x-enterprise-id'],
            'Content-Type': config.headers['Content-Type'],
            'Authorization': config.headers.Authorization ? 'Bearer [TOKEN]' : 'Not set',
            url: config.url,
            method: config.method?.toUpperCase(),
          });
        }

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle errors
    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid, logout user
          store.dispatch(setLogout());
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  async get<T>(endpoint: string): Promise<T> {
    try {
      const response = await this.axiosInstance.get<T>(endpoint);
      return response.data;
    } catch (error) {
      console.error('GET request failed:', error);
      throw error;
    }
  }

  async post<T>(endpoint: string, data?: unknown): Promise<T> {
    try {
      const response = await this.axiosInstance.post<T>(endpoint, data);
      return response.data;
    } catch (error) {
      console.error('POST request failed:', error);
      throw error;
    }
  }

  async put<T>(endpoint: string, data?: unknown): Promise<T> {
    try {
      const response = await this.axiosInstance.put<T>(endpoint, data);
      return response.data;
    } catch (error) {
      console.error('PUT request failed:', error);
      throw error;
    }
  }

  async delete<T>(endpoint: string): Promise<T> {
    try {
      const response = await this.axiosInstance.delete<T>(endpoint);
      return response.data;
    } catch (error) {
      console.error('DELETE request failed:', error);
      throw error;
    }
  }

  // File upload method
  async uploadFile<T>(endpoint: string, file: File, additionalData?: Record<string, unknown>): Promise<T> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      if (additionalData) {
        Object.keys(additionalData).forEach(key => {
          formData.append(key, String(additionalData[key]));
        });
      }

      const response = await this.axiosInstance.post<T>(endpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error) {
      console.error('File upload failed:', error);
      throw error;
    }
  }

  /**
   * Fetches the login configuration from the API
   * @returns The login configuration
   */
  async getLoginConfig(): Promise<LoginConfig> {
    try {
      const response = await this.axiosInstance.get<LoginConfig>('/login-configs');
      console.log('Login config API response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching login configuration:', error);
      throw error;
    }
  }

  /**
   * Gets a presigned URL for file download
   * @param fileName The name of the file
   * @returns The presigned URL
   */
  async getFileDownloadUrl(fileName: string): Promise<string> {
    return this.get<string>(`/files/${fileName}/presigned-url`);
  }
}

const apiService = new ApiService();
export default apiService;
